import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { ProcessService } from './process.service';
import { ProcessRoute, ProcessOperation } from './entities/process-route.entity';
import {
  CreateProcessRouteDto,
  UpdateProcessRouteDto,
  ProcessRouteQueryDto,
  CreateOperationDto,
  UpdateOperationDto,
  CopyProcessRouteDto,
} from './dto/process.dto';

/**
 * 工艺管理控制器
 */
@ApiTags('processes')
@Controller('processes')
@ApiBearerAuth()
export class ProcessController {
  private readonly logger = new Logger(ProcessController.name);

  constructor(private readonly processService: ProcessService) {}

  /**
   * 创建工艺路线
   */
  @Post('routes')
  @ApiOperation({ summary: '创建工艺路线', description: '创建新的工艺路线' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '工艺路线创建成功',
    type: ProcessRoute,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  async createProcessRoute(@Body() createProcessRouteDto: CreateProcessRouteDto): Promise<ProcessRoute> {
    this.logger.log(`创建工艺路线: ${createProcessRouteDto.routeCode}`);
    return await this.processService.createProcessRoute(createProcessRouteDto);
  }

  /**
   * 获取工艺路线列表
   */
  @Get('routes')
  @ApiOperation({ summary: '获取工艺路线列表', description: '分页查询工艺路线列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/ProcessRoute' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  async getProcessRoutes(@Query() query: ProcessRouteQueryDto) {
    this.logger.log(`查询工艺路线列表: 页码=${query.page}, 限制=${query.limit}`);
    return await this.processService.getProcessRoutes(query);
  }

  /**
   * 获取工艺路线详情
   */
  @Get('routes/:id')
  @ApiOperation({ summary: '获取工艺路线详情', description: '根据ID获取工艺路线详细信息' })
  @ApiParam({ name: 'id', description: '工艺路线ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: ProcessRoute,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '工艺路线不存在',
  })
  async getProcessRouteById(@Param('id') id: string): Promise<ProcessRoute> {
    this.logger.log(`获取工艺路线详情: ${id}`);
    return await this.processService.getProcessRouteById(id);
  }

  /**
   * 更新工艺路线
   */
  @Put('routes/:id')
  @ApiOperation({ summary: '更新工艺路线', description: '更新工艺路线信息' })
  @ApiParam({ name: 'id', description: '工艺路线ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新成功',
    type: ProcessRoute,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '工艺路线不存在',
  })
  async updateProcessRoute(
    @Param('id') id: string,
    @Body() updateProcessRouteDto: UpdateProcessRouteDto,
  ): Promise<ProcessRoute> {
    this.logger.log(`更新工艺路线: ${id}`);
    return await this.processService.updateProcessRoute(id, updateProcessRouteDto);
  }

  /**
   * 删除工艺路线
   */
  @Delete('routes/:id')
  @ApiOperation({ summary: '删除工艺路线', description: '删除工艺路线及其关联工序' })
  @ApiParam({ name: 'id', description: '工艺路线ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '工艺路线不存在',
  })
  async deleteProcessRoute(@Param('id') id: string): Promise<void> {
    this.logger.log(`删除工艺路线: ${id}`);
    await this.processService.deleteProcessRoute(id);
  }

  /**
   * 激活工艺路线
   */
  @Post('routes/:id/activate')
  @ApiOperation({ summary: '激活工艺路线', description: '激活工艺路线使其生效' })
  @ApiParam({ name: 'id', description: '工艺路线ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '激活成功',
    type: ProcessRoute,
  })
  async activateProcessRoute(@Param('id') id: string): Promise<ProcessRoute> {
    this.logger.log(`激活工艺路线: ${id}`);
    return await this.processService.activateProcessRoute(id);
  }

  /**
   * 停用工艺路线
   */
  @Post('routes/:id/deactivate')
  @ApiOperation({ summary: '停用工艺路线', description: '停用工艺路线使其失效' })
  @ApiParam({ name: 'id', description: '工艺路线ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '停用成功',
    type: ProcessRoute,
  })
  async deactivateProcessRoute(@Param('id') id: string): Promise<ProcessRoute> {
    this.logger.log(`停用工艺路线: ${id}`);
    return await this.processService.deactivateProcessRoute(id);
  }

  /**
   * 复制工艺路线
   */
  @Post('routes/:id/copy')
  @ApiOperation({ summary: '复制工艺路线', description: '复制现有工艺路线创建新版本' })
  @ApiParam({ name: 'id', description: '工艺路线ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '复制成功',
    type: ProcessRoute,
  })
  async copyProcessRoute(
    @Param('id') id: string,
    @Body() copyProcessRouteDto: CopyProcessRouteDto,
  ): Promise<ProcessRoute> {
    this.logger.log(`复制工艺路线: ${id} -> ${copyProcessRouteDto.newRouteCode}`);
    return await this.processService.copyProcessRoute(
      id,
      copyProcessRouteDto.newRouteCode,
      copyProcessRouteDto.newRouteName,
    );
  }

  /**
   * 添加工序
   */
  @Post('routes/:routeId/operations')
  @ApiOperation({ summary: '添加工序', description: '为工艺路线添加新工序' })
  @ApiParam({ name: 'routeId', description: '工艺路线ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '工序添加成功',
    type: ProcessOperation,
  })
  async addOperation(
    @Param('routeId') routeId: string,
    @Body() createOperationDto: CreateOperationDto,
  ): Promise<ProcessOperation> {
    this.logger.log(`添加工序: ${createOperationDto.operationCode} 到工艺路线 ${routeId}`);
    return await this.processService.addOperation(routeId, createOperationDto);
  }

  /**
   * 更新工序
   */
  @Put('operations/:operationId')
  @ApiOperation({ summary: '更新工序', description: '更新工序信息' })
  @ApiParam({ name: 'operationId', description: '工序ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新成功',
    type: ProcessOperation,
  })
  async updateOperation(
    @Param('operationId') operationId: string,
    @Body() updateOperationDto: UpdateOperationDto,
  ): Promise<ProcessOperation> {
    this.logger.log(`更新工序: ${operationId}`);
    return await this.processService.updateOperation(operationId, updateOperationDto);
  }

  /**
   * 删除工序
   */
  @Delete('operations/:operationId')
  @ApiOperation({ summary: '删除工序', description: '删除工序' })
  @ApiParam({ name: 'operationId', description: '工序ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '删除成功',
  })
  async deleteOperation(@Param('operationId') operationId: string): Promise<void> {
    this.logger.log(`删除工序: ${operationId}`);
    await this.processService.deleteOperation(operationId);
  }

  /**
   * 获取工艺路线统计
   */
  @Get('stats/overview')
  @ApiOperation({ summary: '获取工艺路线统计', description: '获取工艺路线统计数据' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        totalRoutes: { type: 'number' },
        routesByStatus: { type: 'object' },
        avgOperationsPerRoute: { type: 'number' },
        avgStandardTime: { type: 'number' },
        operationsByType: { type: 'object' },
        activeRoutes: { type: 'number' },
      },
    },
  })
  async getProcessRouteStatistics() {
    this.logger.log('获取工艺路线统计数据');
    return await this.processService.getProcessRouteStatistics();
  }
}

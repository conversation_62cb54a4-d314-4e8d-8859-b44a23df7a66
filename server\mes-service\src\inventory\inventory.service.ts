import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Inventory, InventoryTransaction, InventoryType, InventoryStatus } from './entities/inventory.entity';

/**
 * 库存管理服务
 */
@Injectable()
export class InventoryService {
  private readonly logger = new Logger(InventoryService.name);

  constructor(
    @InjectRepository(Inventory)
    private readonly inventoryRepository: Repository<Inventory>,
    @InjectRepository(InventoryTransaction)
    private readonly inventoryTransactionRepository: Repository<InventoryTransaction>,
  ) {}

  /**
   * 创建库存记录
   */
  async createInventory(createInventoryDto: any): Promise<Inventory> {
    try {
      const inventory = this.inventoryRepository.create(createInventoryDto);
      const savedInventory = await this.inventoryRepository.save(inventory);
      this.logger.log(`库存记录创建成功: ${savedInventory.materialCode}`);
      return savedInventory;
    } catch (error) {
      this.logger.error(`创建库存记录失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取库存列表
   */
  async getInventories(query: any) {
    try {
      const { page = 1, limit = 10, materialCode, warehouseCode, inventoryType, status } = query;
      const skip = (page - 1) * limit;

      const queryBuilder = this.inventoryRepository.createQueryBuilder('inventory');

      if (materialCode) {
        queryBuilder.andWhere('inventory.materialCode LIKE :materialCode', { materialCode: `%${materialCode}%` });
      }

      if (warehouseCode) {
        queryBuilder.andWhere('inventory.warehouseCode = :warehouseCode', { warehouseCode });
      }

      if (inventoryType) {
        queryBuilder.andWhere('inventory.inventoryType = :inventoryType', { inventoryType });
      }

      if (status) {
        queryBuilder.andWhere('inventory.status = :status', { status });
      }

      queryBuilder.orderBy('inventory.createdAt', 'DESC');
      queryBuilder.skip(skip).take(limit);

      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`获取库存列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 根据ID获取库存详情
   */
  async getInventoryById(id: string): Promise<Inventory> {
    try {
      const inventory = await this.inventoryRepository.findOne({
        where: { id },
      });

      if (!inventory) {
        throw new NotFoundException(`库存记录不存在: ${id}`);
      }

      return inventory;
    } catch (error) {
      this.logger.error(`获取库存详情失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新库存
   */
  async updateInventory(id: string, updateInventoryDto: any): Promise<Inventory> {
    try {
      const inventory = await this.getInventoryById(id);
      Object.assign(inventory, updateInventoryDto);
      const updatedInventory = await this.inventoryRepository.save(inventory);
      this.logger.log(`库存记录更新成功: ${updatedInventory.materialCode}`);
      return updatedInventory;
    } catch (error) {
      this.logger.error(`更新库存记录失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除库存
   */
  async deleteInventory(id: string): Promise<void> {
    try {
      const inventory = await this.getInventoryById(id);
      await this.inventoryRepository.remove(inventory);
      this.logger.log(`库存记录删除成功: ${inventory.materialCode}`);
    } catch (error) {
      this.logger.error(`删除库存记录失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 入库操作
   */
  async stockIn(materialCode: string, quantity: number, warehouseCode: string, operatedBy: string, reason?: string): Promise<Inventory> {
    try {
      const inventory = await this.inventoryRepository.findOne({
        where: { materialCode, warehouseCode },
      });

      if (!inventory) {
        throw new NotFoundException(`库存记录不存在: ${materialCode} in ${warehouseCode}`);
      }

      const quantityBefore = inventory.currentQuantity;
      inventory.currentQuantity += quantity;
      inventory.availableQuantity += quantity;

      const updatedInventory = await this.inventoryRepository.save(inventory);

      // 记录库存事务
      await this.createTransaction({
        transactionNumber: this.generateTransactionNumber('IN'),
        materialCode,
        transactionType: '入库',
        quantityChange: quantity,
        quantityBefore,
        quantityAfter: inventory.currentQuantity,
        warehouseCode,
        reason,
        operatedBy,
      });

      this.logger.log(`入库成功: ${materialCode}, 数量: ${quantity}`);
      return updatedInventory;
    } catch (error) {
      this.logger.error(`入库操作失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 出库操作
   */
  async stockOut(materialCode: string, quantity: number, warehouseCode: string, operatedBy: string, reason?: string): Promise<Inventory> {
    try {
      const inventory = await this.inventoryRepository.findOne({
        where: { materialCode, warehouseCode },
      });

      if (!inventory) {
        throw new NotFoundException(`库存记录不存在: ${materialCode} in ${warehouseCode}`);
      }

      if (inventory.availableQuantity < quantity) {
        throw new Error(`可用库存不足: 需要 ${quantity}, 可用 ${inventory.availableQuantity}`);
      }

      const quantityBefore = inventory.currentQuantity;
      inventory.currentQuantity -= quantity;
      inventory.availableQuantity -= quantity;

      const updatedInventory = await this.inventoryRepository.save(inventory);

      // 记录库存事务
      await this.createTransaction({
        transactionNumber: this.generateTransactionNumber('OUT'),
        materialCode,
        transactionType: '出库',
        quantityChange: -quantity,
        quantityBefore,
        quantityAfter: inventory.currentQuantity,
        warehouseCode,
        reason,
        operatedBy,
      });

      this.logger.log(`出库成功: ${materialCode}, 数量: ${quantity}`);
      return updatedInventory;
    } catch (error) {
      this.logger.error(`出库操作失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 库存预留
   */
  async reserveStock(materialCode: string, quantity: number, warehouseCode: string, operatedBy: string): Promise<Inventory> {
    try {
      const inventory = await this.inventoryRepository.findOne({
        where: { materialCode, warehouseCode },
      });

      if (!inventory) {
        throw new NotFoundException(`库存记录不存在: ${materialCode} in ${warehouseCode}`);
      }

      if (inventory.availableQuantity < quantity) {
        throw new Error(`可用库存不足: 需要 ${quantity}, 可用 ${inventory.availableQuantity}`);
      }

      inventory.availableQuantity -= quantity;
      inventory.reservedQuantity += quantity;

      const updatedInventory = await this.inventoryRepository.save(inventory);

      // 记录库存事务
      await this.createTransaction({
        transactionNumber: this.generateTransactionNumber('RSV'),
        materialCode,
        transactionType: '预留',
        quantityChange: 0,
        quantityBefore: inventory.currentQuantity,
        quantityAfter: inventory.currentQuantity,
        warehouseCode,
        reason: '库存预留',
        operatedBy,
      });

      this.logger.log(`库存预留成功: ${materialCode}, 数量: ${quantity}`);
      return updatedInventory;
    } catch (error) {
      this.logger.error(`库存预留失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取库存统计
   */
  async getInventoryStatistics() {
    try {
      const totalItems = await this.inventoryRepository.count();
      
      const inventoryByType = await this.inventoryRepository
        .createQueryBuilder('inventory')
        .select('inventory.inventoryType', 'type')
        .addSelect('COUNT(*)', 'count')
        .addSelect('SUM(inventory.currentQuantity * inventory.unitPrice)', 'totalValue')
        .groupBy('inventory.inventoryType')
        .getRawMany();

      const lowStockItems = await this.inventoryRepository
        .createQueryBuilder('inventory')
        .where('inventory.currentQuantity <= inventory.minStock')
        .getCount();

      const expiredItems = await this.inventoryRepository
        .createQueryBuilder('inventory')
        .where('inventory.expiryDate IS NOT NULL AND inventory.expiryDate < NOW()')
        .getCount();

      const totalValue = await this.inventoryRepository
        .createQueryBuilder('inventory')
        .select('SUM(inventory.currentQuantity * inventory.unitPrice)', 'totalValue')
        .getRawOne();

      return {
        totalItems,
        inventoryByType: inventoryByType.reduce((acc, item) => {
          acc[item.type] = {
            count: parseInt(item.count),
            totalValue: parseFloat(item.totalValue) || 0,
          };
          return acc;
        }, {}),
        lowStockItems,
        expiredItems,
        totalValue: parseFloat(totalValue.totalValue) || 0,
      };
    } catch (error) {
      this.logger.error(`获取库存统计失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 创建库存事务记录
   */
  private async createTransaction(transactionData: any): Promise<InventoryTransaction> {
    const transaction = this.inventoryTransactionRepository.create(transactionData);
    return await this.inventoryTransactionRepository.save(transaction);
  }

  /**
   * 生成事务编号
   */
  private generateTransactionNumber(type: string): string {
    const timestamp = new Date().toISOString().replace(/[-:T.]/g, '').slice(0, 14);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${type}${timestamp}${random}`;
  }

  /**
   * 获取库存事务记录
   */
  async getTransactions(query: any) {
    try {
      const { page = 1, limit = 10, materialCode, transactionType } = query;
      const skip = (page - 1) * limit;

      const queryBuilder = this.inventoryTransactionRepository.createQueryBuilder('transaction');

      if (materialCode) {
        queryBuilder.andWhere('transaction.materialCode = :materialCode', { materialCode });
      }

      if (transactionType) {
        queryBuilder.andWhere('transaction.transactionType = :transactionType', { transactionType });
      }

      queryBuilder.orderBy('transaction.operatedAt', 'DESC');
      queryBuilder.skip(skip).take(limit);

      const [data, total] = await queryBuilder.getManyAndCount();

      return {
        data,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`获取库存事务记录失败: ${error.message}`, error.stack);
      throw error;
    }
  }
}

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { OrderService } from './order.service';
import { ProductionOrder } from './entities/production-order.entity';
import {
  CreateOrderDto,
  UpdateOrderDto,
  OrderQueryDto,
  BatchOrderDto,
  OrderStatsQueryDto,
} from './dto/order.dto';

/**
 * 生产订单控制器
 */
@ApiTags('orders')
@Controller('orders')
@ApiBearerAuth()
export class OrderController {
  private readonly logger = new Logger(OrderController.name);

  constructor(private readonly orderService: OrderService) {}

  /**
   * 创建生产订单
   */
  @Post()
  @ApiOperation({ summary: '创建生产订单', description: '创建新的生产订单' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '订单创建成功',
    type: ProductionOrder,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  async createOrder(@Body() createOrderDto: CreateOrderDto): Promise<ProductionOrder> {
    this.logger.log(`创建生产订单: ${createOrderDto.orderNumber}`);
    return await this.orderService.createOrder(createOrderDto);
  }

  /**
   * 获取订单列表
   */
  @Get()
  @ApiOperation({ summary: '获取订单列表', description: '分页查询生产订单列表' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/ProductionOrder' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  async getOrders(@Query() query: OrderQueryDto) {
    this.logger.log(`查询订单列表: 页码=${query.page}, 限制=${query.limit}`);
    return await this.orderService.getOrders(query);
  }

  /**
   * 获取订单详情
   */
  @Get(':id')
  @ApiOperation({ summary: '获取订单详情', description: '根据ID获取生产订单详细信息' })
  @ApiParam({ name: 'id', description: '订单ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    type: ProductionOrder,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '订单不存在',
  })
  async getOrderById(@Param('id') id: string): Promise<ProductionOrder> {
    this.logger.log(`获取订单详情: ${id}`);
    return await this.orderService.getOrderById(id);
  }

  /**
   * 更新订单
   */
  @Put(':id')
  @ApiOperation({ summary: '更新订单', description: '更新生产订单信息' })
  @ApiParam({ name: 'id', description: '订单ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '更新成功',
    type: ProductionOrder,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '订单不存在',
  })
  async updateOrder(
    @Param('id') id: string,
    @Body() updateOrderDto: UpdateOrderDto,
  ): Promise<ProductionOrder> {
    this.logger.log(`更新订单: ${id}`);
    return await this.orderService.updateOrder(id, updateOrderDto);
  }

  /**
   * 删除订单
   */
  @Delete(':id')
  @ApiOperation({ summary: '删除订单', description: '删除生产订单' })
  @ApiParam({ name: 'id', description: '订单ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '删除成功',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '订单不存在',
  })
  async deleteOrder(@Param('id') id: string): Promise<void> {
    this.logger.log(`删除订单: ${id}`);
    await this.orderService.deleteOrder(id);
  }

  /**
   * 启动订单
   */
  @Post(':id/start')
  @ApiOperation({ summary: '启动订单', description: '启动生产订单执行' })
  @ApiParam({ name: 'id', description: '订单ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '启动成功',
    type: ProductionOrder,
  })
  async startOrder(@Param('id') id: string): Promise<ProductionOrder> {
    this.logger.log(`启动订单: ${id}`);
    return await this.orderService.startOrder(id);
  }

  /**
   * 暂停订单
   */
  @Post(':id/pause')
  @ApiOperation({ summary: '暂停订单', description: '暂停生产订单执行' })
  @ApiParam({ name: 'id', description: '订单ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '暂停成功',
    type: ProductionOrder,
  })
  async pauseOrder(@Param('id') id: string): Promise<ProductionOrder> {
    this.logger.log(`暂停订单: ${id}`);
    return await this.orderService.pauseOrder(id);
  }

  /**
   * 完成订单
   */
  @Post(':id/complete')
  @ApiOperation({ summary: '完成订单', description: '标记生产订单为完成状态' })
  @ApiParam({ name: 'id', description: '订单ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '完成成功',
    type: ProductionOrder,
  })
  async completeOrder(@Param('id') id: string): Promise<ProductionOrder> {
    this.logger.log(`完成订单: ${id}`);
    return await this.orderService.completeOrder(id);
  }

  /**
   * 取消订单
   */
  @Post(':id/cancel')
  @ApiOperation({ summary: '取消订单', description: '取消生产订单' })
  @ApiParam({ name: 'id', description: '订单ID', type: 'string' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '取消成功',
    type: ProductionOrder,
  })
  async cancelOrder(@Param('id') id: string): Promise<ProductionOrder> {
    this.logger.log(`取消订单: ${id}`);
    return await this.orderService.cancelOrder(id);
  }

  /**
   * 批量操作订单
   */
  @Post('batch')
  @ApiOperation({ summary: '批量操作订单', description: '批量执行订单操作' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '批量操作成功',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'number' },
        failed: { type: 'number' },
        results: { type: 'array' },
      },
    },
  })
  async batchOperation(@Body() batchOrderDto: BatchOrderDto) {
    this.logger.log(`批量操作订单: ${batchOrderDto.orderIds.length}个订单`);
    return await this.orderService.batchOperation(batchOrderDto);
  }

  /**
   * 获取订单统计
   */
  @Get('stats/overview')
  @ApiOperation({ summary: '获取订单统计', description: '获取订单统计数据' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        totalOrders: { type: 'number' },
        ordersByStatus: { type: 'object' },
        ordersByPriority: { type: 'object' },
        completionRate: { type: 'number' },
        qualityRate: { type: 'number' },
        overdueOrders: { type: 'number' },
        avgCycleTime: { type: 'number' },
      },
    },
  })
  async getOrderStatistics(@Query() query: OrderStatsQueryDto) {
    this.logger.log('获取订单统计数据');
    return await this.orderService.getOrderStatistics(query);
  }

  /**
   * 获取订单趋势
   */
  @Get('stats/trends')
  @ApiOperation({ summary: '获取订单趋势', description: '获取订单趋势分析数据' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '获取成功',
  })
  async getOrderTrends(@Query() query: OrderStatsQueryDto) {
    this.logger.log('获取订单趋势数据');
    return await this.orderService.getOrderTrends(query);
  }

  /**
   * 导出订单数据
   */
  @Get('export')
  @ApiOperation({ summary: '导出订单数据', description: '导出订单数据到Excel文件' })
  @ApiQuery({ name: 'format', enum: ['excel', 'csv'], required: false })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '导出成功',
  })
  async exportOrders(@Query() query: OrderQueryDto, @Query('format') format: string = 'excel') {
    this.logger.log(`导出订单数据: 格式=${format}`);
    return await this.orderService.exportOrders(query, format);
  }
}
